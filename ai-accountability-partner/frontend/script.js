class VoiceChat {
    constructor() {
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.isListening = false;
        this.isSpeaking = false;
        
        this.initElements();
        this.initSpeechRecognition();
        this.bindEvents();
    }
    
    initElements() {
        this.chatContainer = document.getElementById('chatContainer');
        this.talkBtn = document.getElementById('talkBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.status = document.getElementById('status');
    }
    
    initSpeechRecognition() {
        if ('webkitSpeechRecognition' in window) {
            this.recognition = new webkitSpeechRecognition();
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'en-US';
            
            this.recognition.onstart = () => {
                this.isListening = true;
                this.updateStatus('Listening...', 'listening');
                this.toggleButtons(true);
            };
            
            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.handleUserMessage(transcript);
            };
            
            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.updateStatus('Error: ' + event.error, '');
                this.stopListening();
            };
            
            this.recognition.onend = () => {
                this.isListening = false;
                this.toggleButtons(false);
            };
        } else {
            this.updateStatus('Speech recognition not supported in this browser', '');
            this.talkBtn.disabled = true;
        }
    }
    
    bindEvents() {
        this.talkBtn.addEventListener('click', () => this.startListening());
        this.stopBtn.addEventListener('click', () => this.stopListening());
    }
    
    startListening() {
        if (this.recognition && !this.isListening) {
            // Stop any ongoing speech
            this.synthesis.cancel();
            this.recognition.start();
        }
    }
    
    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }
    
    async handleUserMessage(message) {
        this.addMessage(message, 'user');
        this.updateStatus('Processing...', 'processing');
        
        try {
            const response = await fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            });
            
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            
            const data = await response.json();
            this.addMessage(data.response, 'ai');
            this.speak(data.response);
            
        } catch (error) {
            console.error('Error:', error);
            const errorMsg = 'Sorry, I had trouble processing that. Please try again.';
            this.addMessage(errorMsg, 'ai');
            this.speak(errorMsg);
        }
        
        this.updateStatus('Ready to listen', '');
    }
    
    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        messageDiv.textContent = text;
        
        this.chatContainer.appendChild(messageDiv);
        this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
    }
    
    speak(text) {
        if (this.synthesis) {
            // Cancel any ongoing speech
            this.synthesis.cancel();
            
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = 0.9;
            utterance.pitch = 1;
            utterance.volume = 1;
            
            utterance.onstart = () => {
                this.isSpeaking = true;
                this.updateStatus('Speaking...', 'processing');
            };
            
            utterance.onend = () => {
                this.isSpeaking = false;
                this.updateStatus('Ready to listen', '');
            };
            
            utterance.onerror = (event) => {
                console.error('Speech synthesis error:', event.error);
                this.isSpeaking = false;
                this.updateStatus('Ready to listen', '');
            };
            
            this.synthesis.speak(utterance);
        }
    }
    
    updateStatus(message, className) {
        this.status.textContent = message;
        this.status.className = `status ${className}`;
    }
    
    toggleButtons(listening) {
        if (listening) {
            this.talkBtn.style.display = 'none';
            this.stopBtn.style.display = 'inline-block';
        } else {
            this.talkBtn.style.display = 'inline-block';
            this.stopBtn.style.display = 'none';
        }
    }
}

// Initialize the voice chat when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new VoiceChat();
});
