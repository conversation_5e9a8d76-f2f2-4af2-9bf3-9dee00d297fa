<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Accountability Partner</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .chat-container {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            overflow-y: auto;
            background-color: #fafafa;
            margin-bottom: 20px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        
        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        
        .ai-message {
            background-color: #e9ecef;
            color: #333;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .talk-btn {
            background-color: #28a745;
            color: white;
        }
        
        .talk-btn:hover {
            background-color: #218838;
        }
        
        .talk-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .stop-btn {
            background-color: #dc3545;
            color: white;
        }
        
        .stop-btn:hover {
            background-color: #c82333;
        }
        
        .status {
            text-align: center;
            margin-top: 10px;
            font-style: italic;
            color: #666;
        }
        
        .listening {
            color: #28a745;
            font-weight: bold;
        }
        
        .processing {
            color: #007bff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 AI Accountability Partner</h1>
        
        <div class="chat-container" id="chatContainer">
            <div class="message ai-message">
                Hi! I'm your AI accountability partner. I'm here to help you stay focused on your goals and provide support. Click "Start Talking" and tell me what's on your mind!
            </div>
        </div>
        
        <div class="controls">
            <button id="talkBtn" class="talk-btn">🎤 Start Talking</button>
            <button id="stopBtn" class="stop-btn" style="display: none;">⏹️ Stop</button>
        </div>
        
        <div class="status" id="status">Ready to listen</div>
    </div>

    <script src="script.js"></script>
</body>
</html>
