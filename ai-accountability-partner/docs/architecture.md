# High-Level Architecture - AI Accountability Partner MVP

## System Overview
Web-based local application with proactive AI engagement for personal accountability and goal management.

## Core System Components

### 1. Web Interface (Browser)
- **Voice Input**: Web Speech API (`webkitSpeechRecognition`)
- **Voice Output**: Speech Synthesis API (`speechSynthesis`)
- **Real-time Interface**: Always-on connection to backend
- **Simple UI**: Minimal chat interface for voice conversations
- **Access**: `localhost:8000` in browser

### 2. Conversation Engine (Python Backend)
- **Framework**: FastAPI server
- **AI Integration**: Together AI API for natural language processing
- **Context Management**: Maintains conversation flow and user context
- **Voice Processing**: Handles speech-to-text input and generates responses
- **WebSocket/HTTP**: Real-time communication with frontend

### 3. Memory & Persistence Layer
- **Storage Format**: JSON files (no database needed for MVP)
- **Conversation History**: All user interactions and AI responses
- **Goals & Tasks**: User-defined objectives and action items
- **User Preferences**: Reminder settings and engagement preferences
- **Context Data**: User patterns, habits, and behavioral insights

### 4. Proactive Engagement System ⭐
- **Background Scheduler**: APScheduler running continuously
- **Reminder Engine**: Checks for due notifications and engagement triggers
- **Engagement Types**:
  - Time-based reminders ("Appointment in 1 hour")
  - Goal check-ins ("How's your exercise going?")
  - Follow-up accountability ("You said you'd finish X by Friday")
- **Dynamic Configuration**: User can adjust reminder frequency through conversation

### 5. Time & Context Awareness
- **Real-time Clock**: Python datetime integration
- **Date/Time Calculations**: Handles "tomorrow", "next week", relative scheduling
- **User Context Tracking**: Monitors goals, preferences, and interaction patterns
- **Adaptive Behavior**: Learns from user feedback and adjusts engagement style

## System Architecture Flow

```
Browser (Voice I/O) ↔ FastAPI Server ↔ Together AI API
                           ↕
                    Memory Storage (JSON)
                           ↕
                  Background Scheduler
                  (Proactive Reminders)
```

## Proactive Engagement Flow

```
Background Scheduler → Check Due Reminders → 
Generate Notification → Send to Frontend → 
Voice Alert to User → Conversation Continues
```

## Data Flow

### Voice Conversation Flow:
```
User Voice → Browser (Speech-to-Text) → 
HTTP/WebSocket → Python Backend → 
Together AI API → AI Response → 
Browser → Text-to-Speech → User Hears
```

### Reminder/Engagement Flow:
```
Scheduled Time → Background Scheduler → 
Check Memory for Due Items → 
Generate Engagement Message → 
Send to Frontend → Voice Notification
```

## File Structure

```
ai-accountability-partner/
├── docs/
│   ├── executive-summary.md
│   ├── mvp-requirements.md
│   └── architecture.md
├── backend/
│   ├── main.py (FastAPI server)
│   ├── ai_service.py (Together AI integration)
│   ├── memory.py (JSON storage management)
│   ├── scheduler.py (Proactive engagement system)
│   └── requirements.txt
├── frontend/
│   ├── index.html
│   ├── script.js (Web Speech APIs)
│   └── style.css
└── data/
    ├── conversations.json (chat history)
    ├── goals.json (user goals and tasks)
    └── preferences.json (user settings)
```

## Key Technologies

### Backend:
- **FastAPI**: Web server and API framework
- **Together AI SDK**: AI conversation processing
- **APScheduler**: Background task scheduling for reminders
- **Python datetime**: Time awareness and calculations
- **JSON**: Simple file-based storage

### Frontend:
- **Web Speech API**: Voice input recognition
- **Speech Synthesis API**: Text-to-speech output
- **WebSocket/Fetch**: Real-time communication
- **Vanilla JavaScript**: Minimal dependencies

### Communication:
- **HTTP/WebSocket**: Browser ↔ Python server
- **REST API**: Together AI integration
- **Local Files**: Data persistence

## Deployment
- **Local Development**: Run Python server in WSL, access via Windows browser
- **Port**: localhost:8000
- **No External Dependencies**: Fully self-contained local application
- **Cross-Platform**: Works on any OS with Python and modern browser

## Scalability Considerations (Future)
- Current architecture designed for single-user MVP validation
- JSON storage easily replaceable with database for multi-user
- Background scheduler can be replaced with proper job queue
- Local deployment can be moved to cloud infrastructure
- Web interface can be enhanced or replaced with mobile app
