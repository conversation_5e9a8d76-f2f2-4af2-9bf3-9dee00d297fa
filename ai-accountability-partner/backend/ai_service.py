import os
import together
from memory import memory_manager

class AIService:
    def __init__(self):
        # Initialize Together AI client
        api_key = os.getenv("TOGETHER_API_KEY", "a568ec2d249d7c5ad009611fa994d08e57d70b21858cd5cf4a4672bc1c567e88")
        together.api_key = api_key
        self.client = together
        
        # System prompt for the AI accountability partner
        self.system_prompt = """You are a helpful AI accountability partner. Your role is to:
- Help users stay focused on their goals
- Provide encouragement and motivation
- Be supportive and non-judgmental
- Keep conversations natural and friendly
- Remember what users tell you about their goals and tasks
- Help manage tasks - when users mention things they need to do, acknowledge them
- When asked about tasks, provide helpful summaries

Keep responses conversational and concise. You're like a supportive friend who helps with accountability.

Task Management Guidelines:
- When users say "I need to..." or similar, acknowledge the task
- When asked "what do I need to do?" list their pending tasks
- When users say "I finished..." acknowledge their completion
- Be encouraging about task completion"""

    async def get_response(self, user_message: str) -> str:
        """Get AI response for user message with conversation history and task management"""
        try:
            # Process task management
            await self._process_tasks(user_message)

            # Get conversation history for context
            conversation_context = memory_manager.get_context_for_ai(limit=8)

            # Get current tasks for context
            pending_tasks = memory_manager.get_tasks(status="pending")
            task_context = ""
            if pending_tasks:
                task_list = [f"- {task['description']}" for task in pending_tasks]
                task_context = f"\n\nCurrent pending tasks:\n" + "\n".join(task_list)

            # Build prompt with system message, context, tasks, and current message
            prompt = f"System: {self.system_prompt}\n\n{conversation_context}{task_context}\n\nUser: {user_message}\n\nAssistant:"

            response = self.client.Complete.create(
                prompt=prompt,
                model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",  # Free model
                max_tokens=200,
                temperature=0.7,
            )

            ai_response = response['output']['choices'][0]['text'].strip()

            # Save both user message and AI response to memory
            memory_manager.add_message("user", user_message)
            memory_manager.add_message("assistant", ai_response)

            return ai_response
            
        except Exception as e:
            print(f"Error getting AI response: {e}")
            return "Sorry, I'm having trouble connecting right now. Please try again."

    async def _process_tasks(self, user_message: str) -> None:
        """Process task-related content in user message"""
        # Extract new tasks
        new_tasks = memory_manager.extract_tasks_from_message(user_message)
        for task_description in new_tasks:
            memory_manager.add_task(task_description)
            print(f"Added task: {task_description}")

        # Check for task completions
        completions = memory_manager.find_completion_in_message(user_message)
        for completion in completions:
            if memory_manager.complete_task(description_match=completion):
                print(f"Completed task: {completion}")

# Global AI service instance
ai_service = AIService()
