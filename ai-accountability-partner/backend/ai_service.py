import os
from together import Together

class AIService:
    def __init__(self):
        # Initialize Together AI client
        api_key = os.getenv("TOGETHER_API_KEY", "a568ec2d249d7c5ad009611fa994d08e57d70b21858cd5cf4a4672bc1c567e88")
        self.client = Together(api_key=api_key)
        
        # System prompt for the AI accountability partner
        self.system_prompt = """You are a helpful AI accountability partner. Your role is to:
- Help users stay focused on their goals
- Provide encouragement and motivation
- Be supportive and non-judgmental
- Keep conversations natural and friendly
- Remember what users tell you about their goals and tasks

Keep responses conversational and concise. You're like a supportive friend who helps with accountability."""

    async def get_response(self, user_message: str) -> str:
        """Get AI response for user message"""
        try:
            response = self.client.chat.completions.create(
                model="meta-llama/Llama-2-7b-chat-hf",  # Free model
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_message}
                ],
                max_tokens=200,
                temperature=0.7,
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"Error getting AI response: {e}")
            return "Sorry, I'm having trouble connecting right now. Please try again."

# Global AI service instance
ai_service = AIService()
