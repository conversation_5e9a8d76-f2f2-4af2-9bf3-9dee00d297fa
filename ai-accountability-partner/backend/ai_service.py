import os
import together
from memory import memory_manager
from task_analyzer import initialize_task_analyzer

class AIService:
    def __init__(self):
        # Initialize Together AI client
        api_key = os.getenv("TOGETHER_API_KEY", "a568ec2d249d7c5ad009611fa994d08e57d70b21858cd5cf4a4672bc1c567e88")
        together.api_key = api_key
        self.client = together

        # Initialize task analyzer
        print(f"Initializing task analyzer with API key: {api_key[:10]}...")
        initialize_task_analyzer(api_key)
        print("Task analyzer initialization completed")
        
        # System prompt for the AI accountability partner
        self.system_prompt = """You are a helpful AI accountability partner. Keep responses SHORT and to the point (1-2 sentences max).

Your role:
- Acknowledge tasks and deadlines briefly
- List tasks when asked
- Celebrate completions
- Be encouraging but concise

Response style:
- Be direct and helpful
- No long explanations unless asked
- Focus on the immediate need
- Use simple, clear language

When users ask "what do I need to do?" just list their tasks with due dates. Don't give long advice unless they specifically ask for it."""

    async def get_response(self, user_message: str) -> str:
        """Get AI response for user message with conversation history, task management, and time awareness"""
        try:
            # Process task management
            await self._process_tasks(user_message)

            # Get conversation history for context
            conversation_context = memory_manager.get_context_for_ai(limit=8)

            # Get time-aware context (includes current time, overdue tasks, upcoming tasks)
            time_context = memory_manager.get_time_aware_context()

            # Get current tasks for context
            pending_tasks = memory_manager.get_tasks(status="pending")
            task_context = ""
            if pending_tasks:
                task_list = []
                for task in pending_tasks:
                    if task.get('due_date'):
                        task_list.append(f"- {task['description']} (due: {task['due_date']})")
                    else:
                        task_list.append(f"- {task['description']}")
                task_context = f"\n\nCurrent pending tasks:\n" + "\n".join(task_list)

            # Build prompt with system message, context, time awareness, tasks, and current message
            prompt = f"System: {self.system_prompt}\n\n{conversation_context}\n\n{time_context}{task_context}\n\nUser: {user_message}\n\nAssistant:"

            response = self.client.Complete.create(
                prompt=prompt,
                model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",  # Free model
                max_tokens=50,  # Much shorter responses
                temperature=0.7,
            )

            ai_response = response['output']['choices'][0]['text'].strip()

            # Save both user message and AI response to memory
            memory_manager.add_message("user", user_message)
            memory_manager.add_message("assistant", ai_response)

            return ai_response
            
        except Exception as e:
            print(f"Error getting AI response: {e}")
            return "Sorry, I'm having trouble connecting right now. Please try again."

    async def _process_tasks(self, user_message: str) -> None:
        """Process task-related content in user message using LLM analysis"""
        try:
            # Use LLM to analyze the message
            analysis = await memory_manager.analyze_message_for_tasks(user_message)

            # Add new tasks
            for task_data in analysis["tasks"]:
                task = memory_manager.add_task(
                    task_description=task_data["description"],
                    due_date=task_data["due_date"]
                )
                due_info = f" (due: {task_data['due_date']})" if task_data["due_date"] else ""
                print(f"Added task: {task_data['description']}{due_info}")

            # Process task completions
            for completion_desc in analysis["completions"]:
                # Try to find and complete the task
                task = memory_manager.find_task_by_description(completion_desc)
                if task:
                    if memory_manager.complete_task(task_id=task["id"]):
                        print(f"Completed task: {task['description']}")
                else:
                    print(f"Could not find task to complete: {completion_desc}")

        except Exception as e:
            print(f"Error processing tasks: {e}")

# Global AI service instance
ai_service = AIService()
