from fastapi import FastAP<PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
import os
from ai_service import ai_service

app = FastAPI(title="AI Accountability Partner")

# Serve static files (frontend)
frontend_path = os.path.join(os.path.dirname(__file__), "..", "frontend")
app.mount("/static", StaticFiles(directory=frontend_path), name="static")

class ChatMessage(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str

@app.get("/")
async def serve_frontend():
    """Serve the main HTML page"""
    frontend_file = os.path.join(frontend_path, "index.html")
    return FileResponse(frontend_file)

@app.post("/chat", response_model=ChatResponse)
async def chat(message: ChatMessage):
    """Handle chat messages from the frontend"""
    try:
        print(f"Received message: {message.message}")
        
        # Get AI response
        ai_response = await ai_service.get_response(message.message)
        print(f"AI response: {ai_response}")
        
        return ChatResponse(response=ai_response)
        
    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    print("Starting AI Accountability Partner server...")
    print("Open your browser to: http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
