from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
import os
from ai_service import ai_service
from memory import memory_manager

app = FastAPI(title="AI Accountability Partner")

# Serve static files (frontend)
frontend_path = os.path.join(os.path.dirname(__file__), "..", "frontend")
app.mount("/static", StaticFiles(directory=frontend_path), name="static")

# Also serve script.js and other assets directly
@app.get("/script.js")
async def serve_script():
    script_file = os.path.join(frontend_path, "script.js")
    return FileResponse(script_file, media_type="application/javascript")

class ChatMessage(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str

@app.get("/")
async def serve_frontend():
    """Serve the main HTML page"""
    frontend_file = os.path.join(frontend_path, "index.html")
    return FileResponse(frontend_file)

@app.post("/chat", response_model=ChatResponse)
async def chat(message: ChatMessage):
    """Handle chat messages from the frontend"""
    try:
        print(f"Received message: {message.message}")
        
        # Get AI response
        ai_response = await ai_service.get_response(message.message)
        print(f"AI response: {ai_response}")
        
        return ChatResponse(response=ai_response)
        
    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}

@app.get("/memory/stats")
async def get_memory_stats():
    """Get conversation memory statistics"""
    return memory_manager.get_stats()

@app.get("/memory/history")
async def get_conversation_history():
    """Get conversation history"""
    return {"history": memory_manager.get_conversation_history()}

@app.delete("/memory/clear")
async def clear_memory():
    """Clear conversation history"""
    memory_manager.clear_history()
    return {"message": "Conversation history cleared"}

@app.get("/tasks")
async def get_tasks():
    """Get all tasks"""
    return {"tasks": memory_manager.get_tasks()}

@app.get("/tasks/pending")
async def get_pending_tasks():
    """Get pending tasks"""
    return {"tasks": memory_manager.get_tasks(status="pending")}

@app.get("/tasks/completed")
async def get_completed_tasks():
    """Get completed tasks"""
    return {"tasks": memory_manager.get_tasks(status="complete")}

@app.post("/tasks/{task_id}/complete")
async def complete_task(task_id: int):
    """Mark a task as complete"""
    success = memory_manager.complete_task(task_id=task_id)
    if success:
        return {"message": f"Task {task_id} marked as complete"}
    else:
        raise HTTPException(status_code=404, detail="Task not found")

if __name__ == "__main__":
    import uvicorn
    print("Starting AI Accountability Partner server...")
    print("Open your browser to: http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
