import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import task_analyzer

class MemoryManager:
    def __init__(self):
        self.data_dir = os.path.join(os.path.dirname(__file__), "..", "data")
        self.conversations_file = os.path.join(self.data_dir, "conversations.json")
        self.tasks_file = os.path.join(self.data_dir, "tasks.json")

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)

        # Initialize files if they don't exist
        if not os.path.exists(self.conversations_file):
            self._save_conversations([])
        if not os.path.exists(self.tasks_file):
            self._save_tasks([])
    
    def _save_conversations(self, conversations: List[Dict[str, Any]]) -> None:
        """Save conversations to JSON file"""
        try:
            with open(self.conversations_file, 'w') as f:
                json.dump(conversations, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving conversations: {e}")
    
    def _load_conversations(self) -> List[Dict[str, Any]]:
        """Load conversations from JSON file"""
        try:
            with open(self.conversations_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading conversations: {e}")
            return []
    
    def add_message(self, role: str, content: str) -> None:
        """Add a new message to conversation history"""
        conversations = self._load_conversations()
        
        message = {
            "role": role,  # "user" or "assistant"
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        
        conversations.append(message)
        
        # Keep only last 50 messages to prevent file from getting too large
        if len(conversations) > 50:
            conversations = conversations[-50:]
        
        self._save_conversations(conversations)
    
    def get_conversation_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent conversation history"""
        conversations = self._load_conversations()
        
        # Return last 'limit' messages
        return conversations[-limit:] if conversations else []
    
    def get_context_for_ai(self, limit: int = 10) -> str:
        """Get conversation history formatted for AI context"""
        history = self.get_conversation_history(limit)
        
        if not history:
            return "This is the start of our conversation."
        
        context_lines = ["Previous conversation:"]
        for msg in history:
            role = "You" if msg["role"] == "assistant" else "User"
            context_lines.append(f"{role}: {msg['content']}")
        
        return "\n".join(context_lines)
    
    def clear_history(self) -> None:
        """Clear all conversation history"""
        self._save_conversations([])
    
    def get_stats(self) -> Dict[str, Any]:
        """Get conversation statistics"""
        conversations = self._load_conversations()
        
        user_messages = sum(1 for msg in conversations if msg["role"] == "user")
        ai_messages = sum(1 for msg in conversations if msg["role"] == "assistant")
        
        return {
            "total_messages": len(conversations),
            "user_messages": user_messages,
            "ai_messages": ai_messages,
            "first_message": conversations[0]["timestamp"] if conversations else None,
            "last_message": conversations[-1]["timestamp"] if conversations else None
        }

    # Task Management Methods
    def _save_tasks(self, tasks: List[Dict[str, Any]]) -> None:
        """Save tasks to JSON file"""
        try:
            with open(self.tasks_file, 'w') as f:
                json.dump(tasks, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving tasks: {e}")

    def _load_tasks(self) -> List[Dict[str, Any]]:
        """Load tasks from JSON file"""
        try:
            with open(self.tasks_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading tasks: {e}")
            return []



    async def analyze_message_for_tasks(self, message: str) -> Dict[str, Any]:
        """Use LLM to analyze message for tasks and intent"""
        if not task_analyzer.task_analyzer:
            print("Task analyzer not initialized")
            return {"intent": "general", "tasks": [], "completions": []}

        try:
            analysis = await task_analyzer.task_analyzer.analyze_message(message)
            return {
                "intent": analysis.intent,
                "tasks": [{"description": task.description, "due_date": task.due_date} for task in analysis.tasks],
                "completions": [comp.description for comp in analysis.completions]
            }
        except Exception as e:
            print(f"Error analyzing message: {e}")
            return {"intent": "general", "tasks": [], "completions": []}

    def add_task(self, task_description: str, due_date: str = None) -> Dict[str, Any]:
        """Add a new task with optional due date"""
        tasks = self._load_tasks()

        task = {
            "id": len(tasks) + 1,
            "description": task_description,
            "status": "pending",
            "created_at": datetime.now().isoformat(),
            "completed_at": None,
            "due_date": due_date
        }

        tasks.append(task)
        self._save_tasks(tasks)

        return task

    def get_tasks(self, status: str = None) -> List[Dict[str, Any]]:
        """Get tasks, optionally filtered by status"""
        tasks = self._load_tasks()

        if status:
            return [task for task in tasks if task["status"] == status]

        return tasks

    def complete_task(self, task_id: int = None, description_match: str = None) -> bool:
        """Mark a task as complete by ID or description match"""
        tasks = self._load_tasks()

        for task in tasks:
            if task_id and task["id"] == task_id:
                task["status"] = "complete"
                task["completed_at"] = datetime.now().isoformat()
                self._save_tasks(tasks)
                return True
            elif description_match and description_match.lower() in task["description"].lower():
                task["status"] = "complete"
                task["completed_at"] = datetime.now().isoformat()
                self._save_tasks(tasks)
                return True

        return False

    def find_task_by_description(self, description: str) -> Optional[Dict[str, Any]]:
        """Find a task by partial description match"""
        tasks = self.get_tasks(status="pending")
        description_lower = description.lower()

        # Try exact match first
        for task in tasks:
            if task["description"].lower() == description_lower:
                return task

        # Try partial match
        for task in tasks:
            if description_lower in task["description"].lower() or task["description"].lower() in description_lower:
                return task

        return None

    def get_overdue_tasks(self) -> List[Dict[str, Any]]:
        """Get tasks that are overdue"""
        tasks = self.get_tasks(status="pending")
        now = datetime.now()
        overdue = []

        for task in tasks:
            if task.get("due_date"):
                try:
                    due_date = datetime.fromisoformat(task["due_date"])
                    if due_date < now:
                        overdue.append(task)
                except ValueError:
                    continue

        return overdue

    def get_upcoming_tasks(self, days_ahead: int = 3) -> List[Dict[str, Any]]:
        """Get tasks due in the next N days"""
        tasks = self.get_tasks(status="pending")
        now = datetime.now()
        future_cutoff = now + timedelta(days=days_ahead)
        upcoming = []

        for task in tasks:
            if task.get("due_date"):
                try:
                    due_date = datetime.fromisoformat(task["due_date"])
                    if now <= due_date <= future_cutoff:
                        upcoming.append(task)
                except ValueError:
                    continue

        return upcoming

    def get_time_aware_context(self) -> str:
        """Get time-aware context for AI"""
        now = datetime.now()
        context_lines = [f"Current date and time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}"]

        overdue = self.get_overdue_tasks()
        if overdue:
            context_lines.append(f"\nOverdue tasks ({len(overdue)}):")
            for task in overdue[:3]:  # Show max 3 overdue tasks
                due_date = datetime.fromisoformat(task["due_date"])
                days_overdue = (now - due_date).days
                context_lines.append(f"- {task['description']} (overdue by {days_overdue} days)")

        upcoming = self.get_upcoming_tasks()
        if upcoming:
            context_lines.append(f"\nUpcoming tasks ({len(upcoming)}):")
            for task in upcoming[:3]:  # Show max 3 upcoming tasks
                due_date = datetime.fromisoformat(task["due_date"])
                days_until = (due_date - now).days
                if days_until == 0:
                    time_desc = "today"
                elif days_until == 1:
                    time_desc = "tomorrow"
                else:
                    time_desc = f"in {days_until} days"
                context_lines.append(f"- {task['description']} (due {time_desc})")

        return "\n".join(context_lines)

# Global memory manager instance
memory_manager = MemoryManager()
