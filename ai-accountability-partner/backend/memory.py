import json
import os
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

class MemoryManager:
    def __init__(self):
        self.data_dir = os.path.join(os.path.dirname(__file__), "..", "data")
        self.conversations_file = os.path.join(self.data_dir, "conversations.json")
        self.tasks_file = os.path.join(self.data_dir, "tasks.json")

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)

        # Initialize files if they don't exist
        if not os.path.exists(self.conversations_file):
            self._save_conversations([])
        if not os.path.exists(self.tasks_file):
            self._save_tasks([])
    
    def _save_conversations(self, conversations: List[Dict[str, Any]]) -> None:
        """Save conversations to JSON file"""
        try:
            with open(self.conversations_file, 'w') as f:
                json.dump(conversations, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving conversations: {e}")
    
    def _load_conversations(self) -> List[Dict[str, Any]]:
        """Load conversations from JSON file"""
        try:
            with open(self.conversations_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading conversations: {e}")
            return []
    
    def add_message(self, role: str, content: str) -> None:
        """Add a new message to conversation history"""
        conversations = self._load_conversations()
        
        message = {
            "role": role,  # "user" or "assistant"
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        
        conversations.append(message)
        
        # Keep only last 50 messages to prevent file from getting too large
        if len(conversations) > 50:
            conversations = conversations[-50:]
        
        self._save_conversations(conversations)
    
    def get_conversation_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent conversation history"""
        conversations = self._load_conversations()
        
        # Return last 'limit' messages
        return conversations[-limit:] if conversations else []
    
    def get_context_for_ai(self, limit: int = 10) -> str:
        """Get conversation history formatted for AI context"""
        history = self.get_conversation_history(limit)
        
        if not history:
            return "This is the start of our conversation."
        
        context_lines = ["Previous conversation:"]
        for msg in history:
            role = "You" if msg["role"] == "assistant" else "User"
            context_lines.append(f"{role}: {msg['content']}")
        
        return "\n".join(context_lines)
    
    def clear_history(self) -> None:
        """Clear all conversation history"""
        self._save_conversations([])
    
    def get_stats(self) -> Dict[str, Any]:
        """Get conversation statistics"""
        conversations = self._load_conversations()
        
        user_messages = sum(1 for msg in conversations if msg["role"] == "user")
        ai_messages = sum(1 for msg in conversations if msg["role"] == "assistant")
        
        return {
            "total_messages": len(conversations),
            "user_messages": user_messages,
            "ai_messages": ai_messages,
            "first_message": conversations[0]["timestamp"] if conversations else None,
            "last_message": conversations[-1]["timestamp"] if conversations else None
        }

    # Task Management Methods
    def _save_tasks(self, tasks: List[Dict[str, Any]]) -> None:
        """Save tasks to JSON file"""
        try:
            with open(self.tasks_file, 'w') as f:
                json.dump(tasks, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving tasks: {e}")

    def _load_tasks(self) -> List[Dict[str, Any]]:
        """Load tasks from JSON file"""
        try:
            with open(self.tasks_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading tasks: {e}")
            return []

    def parse_time_expression(self, text: str) -> Optional[datetime]:
        """Parse natural language time expressions into datetime objects"""
        text_lower = text.lower()
        now = datetime.now()

        # Today/tomorrow patterns
        if "today" in text_lower:
            return now.replace(hour=23, minute=59, second=59, microsecond=0)
        elif "tomorrow" in text_lower:
            return (now + timedelta(days=1)).replace(hour=23, minute=59, second=59, microsecond=0)

        # Day of week patterns
        days_of_week = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }

        for day_name, day_num in days_of_week.items():
            if day_name in text_lower:
                days_ahead = day_num - now.weekday()
                if days_ahead <= 0:  # Target day already happened this week
                    days_ahead += 7
                return (now + timedelta(days=days_ahead)).replace(hour=23, minute=59, second=59, microsecond=0)

        # Relative time patterns
        if "in" in text_lower:
            # "in 3 days", "in 2 weeks", etc.
            match = re.search(r"in (\d+) (day|days|week|weeks|hour|hours)", text_lower)
            if match:
                number = int(match.group(1))
                unit = match.group(2)

                if "day" in unit:
                    return now + timedelta(days=number)
                elif "week" in unit:
                    return now + timedelta(weeks=number)
                elif "hour" in unit:
                    return now + timedelta(hours=number)

        # "by" patterns - "by Friday", "by tomorrow"
        if "by " in text_lower:
            by_text = text_lower.split("by ")[1]
            return self.parse_time_expression(by_text)

        return None

    def extract_tasks_from_message(self, message: str) -> List[Dict[str, Any]]:
        """Extract potential tasks with due dates from user message using patterns"""
        task_patterns = [
            r"I need to (.+)",
            r"I have to (.+)",
            r"I should (.+)",
            r"I must (.+)",
            r"I want to (.+)",
            r"I plan to (.+)",
            r"I'm going to (.+)",
            r"I'll (.+)",
            r"I will (.+)",
            r"Remember to (.+)",
            r"Don't forget to (.+)",
            r"Make sure I (.+)"
        ]

        tasks = []
        message_lower = message.lower()

        for pattern in task_patterns:
            matches = re.findall(pattern, message_lower, re.IGNORECASE)
            for match in matches:
                # Clean up the task text
                task_text = match.strip().rstrip('.,!?')
                if len(task_text) > 3:  # Only add meaningful tasks
                    # Try to extract due date from the task text
                    due_date = self.parse_time_expression(task_text)

                    # Clean task text by removing time expressions
                    clean_task = re.sub(r'\b(by|today|tomorrow|monday|tuesday|wednesday|thursday|friday|saturday|sunday|in \d+ \w+)\b.*', '', task_text, flags=re.IGNORECASE).strip()

                    tasks.append({
                        "description": clean_task if clean_task else task_text,
                        "due_date": due_date.isoformat() if due_date else None
                    })

        return tasks

    def add_task(self, task_description: str, due_date: str = None) -> Dict[str, Any]:
        """Add a new task with optional due date"""
        tasks = self._load_tasks()

        task = {
            "id": len(tasks) + 1,
            "description": task_description,
            "status": "pending",
            "created_at": datetime.now().isoformat(),
            "completed_at": None,
            "due_date": due_date
        }

        tasks.append(task)
        self._save_tasks(tasks)

        return task

    def get_tasks(self, status: str = None) -> List[Dict[str, Any]]:
        """Get tasks, optionally filtered by status"""
        tasks = self._load_tasks()

        if status:
            return [task for task in tasks if task["status"] == status]

        return tasks

    def complete_task(self, task_id: int = None, description_match: str = None) -> bool:
        """Mark a task as complete by ID or description match"""
        tasks = self._load_tasks()

        for task in tasks:
            if task_id and task["id"] == task_id:
                task["status"] = "complete"
                task["completed_at"] = datetime.now().isoformat()
                self._save_tasks(tasks)
                return True
            elif description_match and description_match.lower() in task["description"].lower():
                task["status"] = "complete"
                task["completed_at"] = datetime.now().isoformat()
                self._save_tasks(tasks)
                return True

        return False

    def find_completion_in_message(self, message: str) -> List[str]:
        """Find task completions in user message"""
        completion_patterns = [
            r"I finished (.+)",
            r"I completed (.+)",
            r"I did (.+)",
            r"I'm done with (.+)",
            r"I've done (.+)",
            r"Completed (.+)",
            r"Finished (.+)",
            r"Done with (.+)"
        ]

        completions = []
        message_lower = message.lower()

        for pattern in completion_patterns:
            matches = re.findall(pattern, message_lower, re.IGNORECASE)
            for match in matches:
                completion = match.strip().rstrip('.,!?')
                if len(completion) > 3:
                    completions.append(completion)

        return completions

    def get_overdue_tasks(self) -> List[Dict[str, Any]]:
        """Get tasks that are overdue"""
        tasks = self.get_tasks(status="pending")
        now = datetime.now()
        overdue = []

        for task in tasks:
            if task.get("due_date"):
                try:
                    due_date = datetime.fromisoformat(task["due_date"])
                    if due_date < now:
                        overdue.append(task)
                except ValueError:
                    continue

        return overdue

    def get_upcoming_tasks(self, days_ahead: int = 3) -> List[Dict[str, Any]]:
        """Get tasks due in the next N days"""
        tasks = self.get_tasks(status="pending")
        now = datetime.now()
        future_cutoff = now + timedelta(days=days_ahead)
        upcoming = []

        for task in tasks:
            if task.get("due_date"):
                try:
                    due_date = datetime.fromisoformat(task["due_date"])
                    if now <= due_date <= future_cutoff:
                        upcoming.append(task)
                except ValueError:
                    continue

        return upcoming

    def get_time_aware_context(self) -> str:
        """Get time-aware context for AI"""
        now = datetime.now()
        context_lines = [f"Current date and time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}"]

        overdue = self.get_overdue_tasks()
        if overdue:
            context_lines.append(f"\nOverdue tasks ({len(overdue)}):")
            for task in overdue[:3]:  # Show max 3 overdue tasks
                due_date = datetime.fromisoformat(task["due_date"])
                days_overdue = (now - due_date).days
                context_lines.append(f"- {task['description']} (overdue by {days_overdue} days)")

        upcoming = self.get_upcoming_tasks()
        if upcoming:
            context_lines.append(f"\nUpcoming tasks ({len(upcoming)}):")
            for task in upcoming[:3]:  # Show max 3 upcoming tasks
                due_date = datetime.fromisoformat(task["due_date"])
                days_until = (due_date - now).days
                if days_until == 0:
                    time_desc = "today"
                elif days_until == 1:
                    time_desc = "tomorrow"
                else:
                    time_desc = f"in {days_until} days"
                context_lines.append(f"- {task['description']} (due {time_desc})")

        return "\n".join(context_lines)

# Global memory manager instance
memory_manager = MemoryManager()
