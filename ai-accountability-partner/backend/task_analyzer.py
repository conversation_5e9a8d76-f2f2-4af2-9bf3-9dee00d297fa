import json
import together
from datetime import datetime, timedelta
from typing import List, Optional
from pydantic import BaseModel, Field

class Task(BaseModel):
    description: str = Field(description="Clear, concise task description")
    due_date: Optional[str] = Field(description="Due date in ISO format (YYYY-MM-DDTHH:MM:SS) if mentioned, null otherwise")

class TaskCompletion(BaseModel):
    description: str = Field(description="Description of the completed task")

class TaskAnalysis(BaseModel):
    intent: str = Field(description="Message intent: 'add_task', 'question', 'completion', 'general'")
    tasks: List[Task] = Field(description="List of tasks to add (empty if none)")
    completions: List[TaskCompletion] = Field(description="List of completed tasks (empty if none)")

class LLMTaskAnalyzer:
    def __init__(self, api_key: str):
        together.api_key = api_key
        self.client = together
    
    def _parse_relative_time(self, time_text: str) -> Optional[str]:
        """Convert relative time expressions to ISO datetime strings"""
        if not time_text:
            return None
            
        now = datetime.now()
        time_lower = time_text.lower()
        
        # Today/tomorrow patterns
        if "today" in time_lower:
            return now.replace(hour=23, minute=59, second=59, microsecond=0).isoformat()
        elif "tomorrow" in time_lower:
            return (now + timedelta(days=1)).replace(hour=23, minute=59, second=59, microsecond=0).isoformat()
        
        # Day of week patterns
        days_of_week = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        
        for day_name, day_num in days_of_week.items():
            if day_name in time_lower:
                days_ahead = day_num - now.weekday()
                if days_ahead <= 0:  # Target day already happened this week
                    days_ahead += 7
                return (now + timedelta(days=days_ahead)).replace(hour=23, minute=59, second=59, microsecond=0).isoformat()
        
        # Relative time patterns
        if "in" in time_lower and ("day" in time_lower or "week" in time_lower):
            import re
            match = re.search(r"in (\d+) (day|days|week|weeks)", time_lower)
            if match:
                number = int(match.group(1))
                unit = match.group(2)
                
                if "day" in unit:
                    return (now + timedelta(days=number)).replace(hour=23, minute=59, second=59, microsecond=0).isoformat()
                elif "week" in unit:
                    return (now + timedelta(weeks=number)).replace(hour=23, minute=59, second=59, microsecond=0).isoformat()
        
        return None
    
    async def analyze_message(self, message: str) -> TaskAnalysis:
        """Analyze a message using LLM to extract tasks and determine intent"""
        
        current_time = datetime.now().strftime("%A, %B %d, %Y at %I:%M %p")
        
        system_prompt = f"""You are a task analysis AI. Analyze user messages to extract tasks and determine intent.

Current date/time: {current_time}

Instructions:
1. Determine the message intent: 'add_task', 'question', 'completion', or 'general'
2. Extract any tasks mentioned (things the user needs/wants/should do)
3. Extract any task completions (things the user finished/completed/did)
4. For due dates, convert relative times to ISO format

Intent Guidelines:
- 'add_task': User mentions things they need to do ("I need to...", "I have to...", etc.)
- 'question': User asks questions ("What do I need to do?", "When is...?", etc.)
- 'completion': User mentions finishing tasks ("I finished...", "I completed...", etc.)
- 'general': General conversation, greetings, etc.

Time Conversion Examples:
- "today" → end of today
- "tomorrow" → end of tomorrow  
- "Friday" → end of this Friday (or next Friday if today is Friday)
- "in 3 days" → 3 days from now

Only respond with valid JSON matching the schema."""

        try:
            # Build prompt for Complete API
            prompt = f"{system_prompt}\n\nUser message: {message}\n\nJSON response:"

            response = self.client.Complete.create(
                prompt=prompt,
                model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                max_tokens=300,
                temperature=0.1,
                stop=["User:", "System:"]
            )

            # Extract JSON from response
            response_text = response['output']['choices'][0]['text'].strip()

            # Try to parse JSON
            if response_text.startswith('{'):
                json_response = json.loads(response_text)
            else:
                # If not JSON, try to extract JSON from the text
                import re
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    json_response = json.loads(json_match.group())
                else:
                    raise ValueError("No JSON found in response")

            analysis = TaskAnalysis(**json_response)
            
            # Post-process due dates to ensure they're properly formatted
            for task in analysis.tasks:
                if task.due_date:
                    # Try to parse and reformat the due date
                    parsed_date = self._parse_relative_time(task.due_date)
                    if parsed_date:
                        task.due_date = parsed_date
            
            return analysis
            
        except Exception as e:
            print(f"Error in LLM task analysis: {e}")
            # Fallback to basic analysis
            return TaskAnalysis(
                intent="general",
                tasks=[],
                completions=[]
            )

# Global analyzer instance (will be initialized with API key)
task_analyzer = None

def initialize_task_analyzer(api_key: str):
    global task_analyzer
    print(f"Creating LLMTaskAnalyzer with API key: {api_key[:10]}...")
    task_analyzer = LLMTaskAnalyzer(api_key)
    print(f"Task analyzer created: {task_analyzer}")
